# 量化交易系统

一个功能完整的量化交易监控系统，专注于数据获取、策略分析、风险管理和AI辅助决策，支持A股+港股市场监控。

## 项目定位
- **监控导向**：专注于数据分析和信号生成，不执行实际交易
- **AI增强**：集成DeepSeek API提供智能分析和风险预警
- **多市场支持**：覆盖A股和港股市场
- **用户友好**：支持自助添加股票代码和自定义监控

## 功能特性

### 数据管理
- 实时/历史行情数据获取（支持Tushare等数据源）
- 基本面数据获取（财务、估值指标）
- 数据清洗和质量控制
- 高效时序数据存储

### 策略研究
- 内置常用技术指标和因子库
- 支持自定义因子开发
- 高精度事件驱动回测引擎
- 参数优化和过拟合检测

### 风险管理
- 实时风险监控
- 头寸限制和止损控制
- 风险指标计算（VaR、最大回撤等）
- 熔断机制

### AI辅助分析（DeepSeek API集成）
**第一优先级功能：**
- **实时风险监控**：扫描新闻、公告识别风险事件
- **市场情绪分析**：解析财经新闻/社交媒体情感倾向，生成情绪指数
- **自然语言报告**：自动生成每日复盘摘要

**第二优先级功能：**
- **另类数据解析**：解构非结构化数据生成实时舆情因子
- **策略信号辅助**：基于历史数据训练波动率预测模型
- **黑天鹅语义检测**：识别极端风险事件
- **组合压力测试**：模拟极端市场条件
- **亏损交易诊断**：分析交易失败原因

### 监控功能
**核心监控指标：**
- **价格变动监控**：实时价格、涨跌幅、异常波动提醒
- **技术指标监控**：
  - 趋势策略：双均线交叉、MACD、布林带突破
  - 均值回归：RSI超买超卖、乖离率(BIAS)
- **策略信号生成**：买入/卖出信号、止损/止盈提醒
- **风险指标监控**：
  - 账户级风控：单日最大回撤、单股持仓占比、杠杆率
  - 策略级风控：策略失效检测、滑点监控、黑名单机制
- **市场情绪监控**：基于AI分析的情绪指数
- **基本面数据监控**：财务指标异常、估值变化
- **系统健康监控**：数据质量、API状态、安全监控

**扩展监控功能：**
- 社交舆情监控（雪球等平台）
- 高管行为监控
- 资金流向分析

### 可视化界面
- 实时监控仪表盘
- 策略绩效分析图表
- 交互式数据可视化
- AI生成的自动报告

## 技术架构

### 后端 (Python)
- FastAPI - Web框架
- SQLAlchemy - ORM
- PostgreSQL - 关系型数据库
- InfluxDB - 时序数据库
- Celery - 异步任务队列
- Redis - 缓存和消息队列

### 前端 (React)
- React 18 + TypeScript
- Ant Design - UI组件库
- ECharts - 图表可视化
- WebSocket - 实时数据通信

### 数据源
- **Tushare Pro** - 主要数据源（A股+港股）
  - Token: 772e043e246ef24189447f73f0c276e7fc98f18a0cb7cce72bd0f28d
- **扩展数据源**（可选）
  - 万得(Wind) - 机构级数据质量，成本较高
  - 米筐(RiceQuant) - 性价比较好，API稳定
- **社交舆情数据**
  - 雪球等社交媒体数据爬取（扩展功能）

## 项目结构

```
量化交易系统/
├── backend/                 # 后端代码
│   ├── app/                # 应用主目录
│   │   ├── api/           # API路由
│   │   ├── core/          # 核心配置
│   │   ├── models/        # 数据模型
│   │   ├── services/      # 业务逻辑
│   │   └── utils/         # 工具函数
│   ├── data/              # 数据处理模块
│   ├── strategy/          # 策略引擎
│   ├── risk/              # 风险管理
│   └── requirements.txt   # Python依赖
├── frontend/              # 前端代码
│   ├── src/              # 源代码
│   │   ├── components/   # 组件
│   │   ├── pages/        # 页面
│   │   ├── services/     # API服务
│   │   └── utils/        # 工具函数
│   └── package.json      # Node.js依赖
├── config/               # 配置文件
├── docs/                 # 文档
└── docker-compose.yml    # Docker配置
```

## 快速开始

### 环境要求
- Python 3.9+
- Node.js 16+
- PostgreSQL 13+
- InfluxDB 2.0+
- Redis 6+

### 安装步骤

1. 克隆项目
```bash
git clone <repository-url>
cd 量化交易系统
```

2. 后端设置
```bash
cd backend
pip install -r requirements.txt
```

3. 前端设置
```bash
cd frontend
npm install
```

4. 数据库设置
```bash
# 启动数据库服务
docker-compose up -d postgres influxdb redis
```

5. 启动服务
```bash
# 后端
cd backend
uvicorn app.main:app --reload

# 前端
cd frontend
npm start
```

## 配置说明

### 数据源配置
在 `config/data_sources.yml` 中配置数据源API密钥：

```yaml
tushare:
  token: "772e043e246ef24189447f73f0c276e7fc98f18a0cb7cce72bd0f28d"
  pro_api: true
  markets: ["A股", "港股"]

deepseek:
  api_key: "your_deepseek_api_key"
  base_url: "https://api.deepseek.com"

# 可选扩展数据源
wind:
  username: "your_wind_username"
  password: "your_wind_password"

ricequant:
  api_key: "your_ricequant_api_key"
```

### 数据库配置
在 `config/database.yml` 中配置数据库连接：

```yaml
postgresql:
  host: localhost
  port: 5432
  database: quant_trading
  username: postgres
  password: password

influxdb:
  url: http://localhost:8086
  token: your_influxdb_token
  org: your_org
  bucket: market_data
```

## 开发计划

### Phase 1: 基础架构搭建
- [ ] 基础项目结构创建
- [ ] 数据库设计和初始化（PostgreSQL + InfluxDB）
- [ ] Tushare API集成和A股数据获取
- [ ] 基础数据清洗和存储模块

### Phase 2: 核心功能开发
- [ ] 技术指标计算引擎
- [ ] 风控模块开发
- [ ] 基础可视化界面（React + Ant Design）
- [ ] 实时数据更新机制

### Phase 3: AI功能集成
- [ ] DeepSeek API集成
- [ ] 实时风险监控（新闻分析）
- [ ] 市场情绪分析
- [ ] 自动报告生成

### Phase 4: 扩展功能
- [ ] 港股数据支持
- [ ] 高级AI功能（黑天鹅检测、压力测试）
- [ ] 社交舆情爬取
- [ ] 高级可视化功能

### Phase 5: 优化和扩展
- [ ] 性能优化
- [ ] 更多数据源接入（万得、米筐）
- [ ] 移动端适配
- [ ] 高级分析功能

## 使用指南

### 自助添加股票代码
1. 登录系统
2. 进入"股票管理"页面
3. 点击"添加股票"
4. 输入股票代码（支持A股/港股）
5. 系统自动获取数据并开始监控

### 监控设置
1. 选择监控的股票池
2. 配置技术指标参数
3. 设置风险阈值和告警条件
4. 启动实时监控

### AI分析功能
1. 查看实时风险预警
2. 阅读AI生成的市场分析报告
3. 监控情绪指数变化
4. 获取策略建议

## 技术要求和注意事项

### 数据源选择考虑
- **Tushare Pro**：已配置token，支持A股+港股，优先使用
- **万得(Wind)**：机构级数据质量，年费数万元，适合高端需求
- **米筐(RiceQuant)**：性价比较好，API稳定，可作为备选
- **社交数据**：需考虑反爬虫和合规性问题

### DeepSeek API使用策略
- 避免替代人工交易决策
- 重点用于风险识别和情绪分析
- 控制API调用频率和成本
- 确保数据隐私和安全

### 系统性能要求
- 实时数据更新频率：1-5分钟
- 支持同时监控数百只股票
- 低延迟风险预警机制
- 高可用性和容错处理

## 开发指南

### 添加新的数据源
1. 在 `backend/data/sources/` 下创建新的数据源类
2. 实现标准的数据接口
3. 在配置文件中添加相关配置
4. 更新前端数据源选择组件

### 开发自定义监控指标
1. 在 `backend/strategy/indicators/` 下创建指标类
2. 继承基础指标类并实现计算逻辑
3. 添加指标描述和参数配置
4. 在前端监控面板中注册新指标

### 扩展AI分析功能
1. 在 `backend/ai/` 下添加新的分析模块
2. 集成DeepSeek API调用
3. 实现数据预处理和结果解析
4. 在前端添加对应的展示组件

## 项目沟通记录

### 需求确认
- **市场范围**：A股 + 港股
- **系统定位**：监控导向，不执行实际交易
- **数据源**：Tushare Pro（已配置token）+ 可选扩展数据源
- **AI功能**：DeepSeek API集成，专注风险监控和情绪分析
- **用户功能**：支持自助添加股票代码

### 开发优先级
1. **Phase 1**：基础架构 + A股数据获取 + 基础监控
2. **Phase 2**：技术指标计算 + 风控模块 + 可视化界面
3. **Phase 3**：DeepSeek API集成（风险监控和情绪分析优先）
4. **Phase 4**：港股支持 + 高级AI功能
5. **Phase 5**：社交舆情爬取 + 扩展功能

### 待确认问题
- [ ] DeepSeek API调用频率限制
- [ ] 实时数据更新频率偏好
- [ ] 历史数据回溯时间范围
- [ ] 同时监控股票数量预期
- [ ] 港股数据权限确认
- [ ] 社交舆情数据具体需求

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 联系方式

如有问题，请通过Issue或邮件联系。
