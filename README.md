# 量化交易系统

一个功能完整的量化交易系统，支持数据获取、策略回测、风险管理和可视化分析。

## 功能特性

### 数据管理
- 实时/历史行情数据获取（支持Tushare等数据源）
- 基本面数据获取（财务、估值指标）
- 数据清洗和质量控制
- 高效时序数据存储

### 策略研究
- 内置常用技术指标和因子库
- 支持自定义因子开发
- 高精度事件驱动回测引擎
- 参数优化和过拟合检测

### 风险管理
- 实时风险监控
- 头寸限制和止损控制
- 风险指标计算（VaR、最大回撤等）
- 熔断机制

### AI辅助分析
- 集成DeepSeek API
- 智能市场分析
- 策略建议和优化

### 可视化界面
- 实时监控仪表盘
- 策略绩效分析图表
- 交互式数据可视化
- 自动报告生成

## 技术架构

### 后端 (Python)
- FastAPI - Web框架
- SQLAlchemy - ORM
- PostgreSQL - 关系型数据库
- InfluxDB - 时序数据库
- Celery - 异步任务队列
- Redis - 缓存和消息队列

### 前端 (React)
- React 18 + TypeScript
- Ant Design - UI组件库
- ECharts - 图表可视化
- WebSocket - 实时数据通信

### 数据源
- Tushare Pro - 股票数据
- 其他数据源API接口

## 项目结构

```
量化交易系统/
├── backend/                 # 后端代码
│   ├── app/                # 应用主目录
│   │   ├── api/           # API路由
│   │   ├── core/          # 核心配置
│   │   ├── models/        # 数据模型
│   │   ├── services/      # 业务逻辑
│   │   └── utils/         # 工具函数
│   ├── data/              # 数据处理模块
│   ├── strategy/          # 策略引擎
│   ├── risk/              # 风险管理
│   └── requirements.txt   # Python依赖
├── frontend/              # 前端代码
│   ├── src/              # 源代码
│   │   ├── components/   # 组件
│   │   ├── pages/        # 页面
│   │   ├── services/     # API服务
│   │   └── utils/        # 工具函数
│   └── package.json      # Node.js依赖
├── config/               # 配置文件
├── docs/                 # 文档
└── docker-compose.yml    # Docker配置
```

## 快速开始

### 环境要求
- Python 3.9+
- Node.js 16+
- PostgreSQL 13+
- InfluxDB 2.0+
- Redis 6+

### 安装步骤

1. 克隆项目
```bash
git clone <repository-url>
cd 量化交易系统
```

2. 后端设置
```bash
cd backend
pip install -r requirements.txt
```

3. 前端设置
```bash
cd frontend
npm install
```

4. 数据库设置
```bash
# 启动数据库服务
docker-compose up -d postgres influxdb redis
```

5. 启动服务
```bash
# 后端
cd backend
uvicorn app.main:app --reload

# 前端
cd frontend
npm start
```

## 配置说明

### 数据源配置
在 `config/data_sources.yml` 中配置数据源API密钥：

```yaml
tushare:
  token: "your_tushare_token"
  
deepseek:
  api_key: "your_deepseek_api_key"
  base_url: "https://api.deepseek.com"
```

### 数据库配置
在 `config/database.yml` 中配置数据库连接：

```yaml
postgresql:
  host: localhost
  port: 5432
  database: quant_trading
  username: postgres
  password: password

influxdb:
  url: http://localhost:8086
  token: your_influxdb_token
  org: your_org
  bucket: market_data
```

## 使用指南

### 添加股票代码
1. 登录系统
2. 进入"股票管理"页面
3. 点击"添加股票"
4. 输入股票代码和名称
5. 系统将自动获取相关数据

### 创建策略
1. 进入"策略管理"页面
2. 选择策略模板或创建自定义策略
3. 配置策略参数
4. 运行回测验证
5. 启动实时监控

### 风险监控
1. 在"风险管理"页面设置风险参数
2. 配置止损和头寸限制
3. 监控实时风险指标
4. 设置告警通知

## 开发指南

### 添加新的数据源
1. 在 `backend/data/sources/` 下创建新的数据源类
2. 实现标准的数据接口
3. 在配置文件中添加相关配置
4. 更新前端数据源选择组件

### 开发自定义因子
1. 在 `backend/strategy/factors/` 下创建因子类
2. 继承基础因子类并实现计算逻辑
3. 添加因子描述和参数配置
4. 在前端因子库中注册新因子

### 扩展API接口
1. 在 `backend/app/api/` 下添加新的路由
2. 实现相应的服务逻辑
3. 更新API文档
4. 在前端添加对应的服务调用

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 联系方式

如有问题，请通过Issue或邮件联系。
